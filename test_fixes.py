#!/usr/bin/env python3
"""
اختبار الإصلاحات المطبقة
Test Applied Fixes
"""

import os
import sys
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

def test_environment_variables():
    """اختبار متغيرات البيئة"""
    print("🔍 اختبار متغيرات البيئة:")
    
    required_vars = [
        'BOT_TOKEN',
        'ADMIN_CHAT_ID', 
        'SUPABASE_URL',
        'SUPABASE_KEY'
    ]
    
    all_ok = True
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"  ✅ {var}: موجود")
        else:
            print(f"  ❌ {var}: مفقود")
            all_ok = False
    
    return all_ok

def test_imports():
    """اختبار الاستيرادات"""
    print("\n🔍 اختبار الاستيرادات:")
    
    imports_to_test = [
        ('security_config', 'نظام الحماية'),
        ('secure_config', 'الإعدادات الآمنة'),
        ('security_enhancements', 'التحسينات الأمنية'),
        ('network_config', 'إعدادات الشبكة'),
        ('supabase_client', 'عميل Supabase'),
        ('comprehensive_security', 'النظام الأمني الشامل')
    ]
    
    all_ok = True
    for module_name, description in imports_to_test:
        try:
            __import__(module_name)
            print(f"  ✅ {description}: تم التحميل بنجاح")
        except ImportError as e:
            print(f"  ❌ {description}: فشل التحميل - {e}")
            all_ok = False
        except Exception as e:
            print(f"  ⚠️ {description}: تحذير - {e}")
    
    return all_ok

def test_network_connectivity():
    """اختبار الاتصال بالشبكة"""
    print("\n🔍 اختبار الاتصال بالشبكة:")
    
    try:
        from network_config import check_network_connectivity, check_telegram_connectivity
        
        # اختبار الاتصال العام
        if check_network_connectivity():
            print("  ✅ الاتصال بالإنترنت: يعمل")
        else:
            print("  ❌ الاتصال بالإنترنت: لا يعمل")
            return False
        
        # اختبار اتصال Telegram
        if check_telegram_connectivity():
            print("  ✅ الاتصال مع Telegram: يعمل")
        else:
            print("  ❌ الاتصال مع Telegram: لا يعمل")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في اختبار الشبكة: {e}")
        return False

def test_supabase_connection():
    """اختبار الاتصال مع Supabase"""
    print("\n🔍 اختبار الاتصال مع Supabase:")
    
    try:
        from supabase_client import test_supabase_connection
        
        if test_supabase_connection():
            print("  ✅ الاتصال مع Supabase: يعمل")
            return True
        else:
            print("  ❌ الاتصال مع Supabase: لا يعمل")
            return False
            
    except Exception as e:
        print(f"  ❌ خطأ في اختبار Supabase: {e}")
        return False

def test_security_systems():
    """اختبار أنظمة الحماية"""
    print("\n🔍 اختبار أنظمة الحماية:")
    
    try:
        from security_config import security_check, InputValidator
        from security_enhancements import security_manager
        
        print("  ✅ نظام الحماية الأساسي: يعمل")
        print("  ✅ التحسينات الأمنية: تعمل")
        
        # اختبار تنظيف البيانات
        test_data = "<script>alert('test')</script>Hello World"
        cleaned = InputValidator.sanitize_text(test_data)
        if "<script>" not in cleaned:
            print("  ✅ تنظيف البيانات: يعمل")
        else:
            print("  ❌ تنظيف البيانات: لا يعمل")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في اختبار أنظمة الحماية: {e}")
        return False

def test_file_existence():
    """اختبار وجود الملفات المطلوبة"""
    print("\n🔍 اختبار وجود الملفات:")
    
    required_files = [
        'security_config.py',
        'secure_config.py', 
        'security_enhancements.py',
        'comprehensive_security.py',
        'network_config.py',
        'mod_details.html',
        'auto_fix_network.py',
        'security_config.json',
        '.env'
    ]
    
    all_ok = True
    for file_name in required_files:
        if os.path.exists(file_name):
            print(f"  ✅ {file_name}: موجود")
        else:
            print(f"  ❌ {file_name}: مفقود")
            all_ok = False
    
    return all_ok

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار الإصلاحات المطبقة")
    print("=" * 50)
    
    tests = [
        ("متغيرات البيئة", test_environment_variables),
        ("وجود الملفات", test_file_existence),
        ("الاستيرادات", test_imports),
        ("الاتصال بالشبكة", test_network_connectivity),
        ("الاتصال مع Supabase", test_supabase_connection),
        ("أنظمة الحماية", test_security_systems)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"\n❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # عرض النتائج النهائية
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nالنتيجة النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الإصلاحات تعمل بشكل صحيح!")
        return True
    else:
        print("⚠️ بعض المشاكل لا تزال موجودة")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
