# إعدادات الشبكة المحسنة لحل مشاكل الاتصال
# Enhanced Network Configuration for Connection Issues

import os
import logging
import asyncio
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

# إعدادات الشبكة الأساسية
NETWORK_CONFIG = {
    # إعدادات timeout محسنة لحل مشاكل الاتصال
    'timeouts': {
        'connect_timeout': 120,  # زيادة timeout للاتصال
        'read_timeout': 120,     # زيادة timeout للقراءة
        'write_timeout': 120,    # زيادة timeout للكتابة
        'pool_timeout': 120,     # زيادة timeout للمجموعة
        'dns_timeout': 30,       # timeout خاص بـ DNS
    },

    # إعدادات إعادة المحاولة المحسنة
    'retry': {
        'max_retries': 10,       # زيادة عدد المحاولات
        'retry_delay': 5,        # زيادة التأخير بين المحاولات
        'backoff_factor': 2.0,   # زيادة معامل التأخير
        'max_delay': 60,         # زيادة الحد الأقصى للتأخير
        'exponential_backoff': True,
    },

    # إعدادات مجموعة الاتصالات المحسنة
    'connection_pool': {
        'pool_size': 20,         # زيادة حجم المجموعة
        'max_overflow': 50,      # زيادة الحد الأقصى للفائض
        'pool_timeout': 60,      # زيادة timeout للمجموعة
        'pool_recycle': 1800,    # تقليل وقت إعادة التدوير
        'pool_pre_ping': True,   # فحص الاتصالات قبل الاستخدام
    },

    # إعدادات DNS محسنة
    'dns': {
        'timeout': 30,           # زيادة timeout لـ DNS
        'servers': [             # خوادم DNS متعددة للاحتياط
            '8.8.8.8',          # Google DNS Primary
            '8.8.4.4',          # Google DNS Secondary
            '1.1.1.1',          # Cloudflare DNS Primary
            '1.0.0.1',          # Cloudflare DNS Secondary
            '208.67.222.222',   # OpenDNS Primary
            '208.67.220.220',   # OpenDNS Secondary
        ],
        'fallback_enabled': True,
        'cache_enabled': True,
    },

    # إعدادات البروكسي
    'proxy': {
        'enabled': False,
        'http_proxy': os.environ.get('HTTP_PROXY', ''),
        'https_proxy': os.environ.get('HTTPS_PROXY', ''),
        'no_proxy': os.environ.get('NO_PROXY', ''),
        'auto_detect': True,     # كشف تلقائي للبروكسي
    },

    # إعدادات جديدة لحل مشاكل الشبكة
    'network_fixes': {
        'enable_ipv6': False,    # تعطيل IPv6 لتجنب مشاكل الاتصال
        'force_ipv4': True,      # إجبار استخدام IPv4
        'tcp_keepalive': True,   # تفعيل TCP keepalive
        'tcp_nodelay': True,     # تفعيل TCP nodelay
        'socket_options': True,  # تفعيل خيارات socket محسنة
    }
}

def get_network_config() -> Dict[str, Any]:
    """الحصول على إعدادات الشبكة"""
    return NETWORK_CONFIG.copy()

def get_telegram_request_config() -> Dict[str, Any]:
    """الحصول على إعدادات طلبات Telegram محسنة"""
    config = get_network_config()
    
    return {
        'connection_pool_size': config['connection_pool']['pool_size'],
        'proxy_url': get_proxy_url(),
        'read_timeout': config['timeouts']['read_timeout'],
        'write_timeout': config['timeouts']['write_timeout'],
        'connect_timeout': config['timeouts']['connect_timeout'],
        'pool_timeout': config['timeouts']['pool_timeout'],
    }

def get_proxy_url() -> Optional[str]:
    """الحصول على رابط البروكسي إذا كان متوفراً"""
    proxy_config = NETWORK_CONFIG['proxy']
    
    if not proxy_config['enabled']:
        return None
    
    # أولوية للـ HTTPS proxy
    if proxy_config['https_proxy']:
        return proxy_config['https_proxy']
    
    # ثم HTTP proxy
    if proxy_config['http_proxy']:
        return proxy_config['http_proxy']
    
    return None

def apply_network_fixes():
    """تطبيق إصلاحات الشبكة لحل مشاكل الاتصال"""
    import socket

    try:
        # تطبيق إعدادات socket محسنة
        if NETWORK_CONFIG['network_fixes']['socket_options']:
            # تعيين خيارات socket افتراضية
            socket.setdefaulttimeout(NETWORK_CONFIG['timeouts']['connect_timeout'])

        # تفعيل IPv4 فقط إذا كان مطلوباً
        if NETWORK_CONFIG['network_fixes']['force_ipv4']:
            # تعيين عائلة العناوين إلى IPv4 فقط
            original_getaddrinfo = socket.getaddrinfo

            def ipv4_getaddrinfo(host, port, family=0, type=0, proto=0, flags=0):
                return original_getaddrinfo(host, port, socket.AF_INET, type, proto, flags)

            socket.getaddrinfo = ipv4_getaddrinfo
            logger.info("✅ تم تفعيل IPv4 فقط")

        # تطبيق إعدادات Windows المحسنة
        if os.name == 'nt':  # Windows
            apply_windows_network_fixes()

        logger.info("✅ تم تطبيق إصلاحات الشبكة")
        return True

    except Exception as e:
        logger.error(f"❌ فشل في تطبيق إصلاحات الشبكة: {e}")
        return False

def apply_windows_network_fixes():
    """تطبيق إصلاحات خاصة بـ Windows"""
    try:
        import subprocess

        # تنظيف DNS cache
        try:
            subprocess.run(['ipconfig', '/flushdns'],
                         capture_output=True, timeout=30, check=False)
            logger.info("✅ تم تنظيف DNS cache")
        except Exception:
            pass

        # إعادة تعيين Winsock
        try:
            subprocess.run(['netsh', 'winsock', 'reset'],
                         capture_output=True, timeout=30, check=False)
            logger.info("✅ تم إعادة تعيين Winsock")
        except Exception:
            pass

        logger.info("✅ تم تطبيق تحسينات Windows")

    except Exception as e:
        logger.warning(f"⚠️ فشل في تطبيق تحسينات Windows: {e}")

def check_network_connectivity() -> bool:
    """فحص الاتصال بالشبكة مع إعدادات محسنة"""
    import socket

    dns_servers = NETWORK_CONFIG['dns']['servers']
    timeout = NETWORK_CONFIG['dns']['timeout']

    # تطبيق إصلاحات الشبكة أولاً
    apply_network_fixes()

    for server in dns_servers:
        try:
            # استخدام IPv4 فقط
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)

            # تطبيق خيارات socket محسنة
            if NETWORK_CONFIG['network_fixes']['tcp_keepalive']:
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)

            if NETWORK_CONFIG['network_fixes']['tcp_nodelay']:
                sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)

            result = sock.connect_ex((server, 53))
            sock.close()

            if result == 0:
                logger.info(f"✅ نجح الاتصال مع DNS server: {server}")
                return True
            else:
                logger.warning(f"⚠️ فشل الاتصال مع DNS server {server}: {result}")

        except OSError as e:
            logger.warning(f"⚠️ فشل الاتصال مع DNS server {server}: {e}")
            continue
        except Exception as e:
            logger.error(f"❌ خطأ غير متوقع مع DNS server {server}: {e}")
            continue
    
    logger.error("❌ فشل في الاتصال مع جميع DNS servers")
    return False

def check_telegram_connectivity() -> bool:
    """فحص الاتصال مع خوادم Telegram مع إعدادات محسنة"""
    import socket

    telegram_servers = [
        ('api.telegram.org', 443),
        ('api.telegram.org', 80),
        ('**************', 443),  # IP مباشر لـ Telegram
        ('**************', 443),  # IP احتياطي
    ]

    timeout = NETWORK_CONFIG['timeouts']['connect_timeout']

    for server, port in telegram_servers:
        try:
            # استخدام IPv4 فقط مع خيارات محسنة
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)

            # تطبيق خيارات socket محسنة
            if NETWORK_CONFIG['network_fixes']['tcp_keepalive']:
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)

            if NETWORK_CONFIG['network_fixes']['tcp_nodelay']:
                sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)

            result = sock.connect_ex((server, port))
            sock.close()

            if result == 0:
                logger.info(f"✅ نجح الاتصال مع Telegram server: {server}:{port}")
                return True
            else:
                logger.warning(f"⚠️ فشل الاتصال مع Telegram server {server}:{port}: {result}")

        except OSError as e:
            logger.warning(f"⚠️ فشل الاتصال مع Telegram server {server}:{port}: {e}")
            continue
        except Exception as e:
            logger.error(f"❌ خطأ غير متوقع مع Telegram server {server}:{port}: {e}")
            continue
    
    logger.error("❌ فشل في الاتصال مع خوادم Telegram")
    return False

async def test_telegram_api(token: str) -> bool:
    """اختبار الاتصال مع Telegram API"""
    try:
        import aiohttp
        
        config = get_network_config()
        timeout = aiohttp.ClientTimeout(
            total=config['timeouts']['read_timeout'],
            connect=config['timeouts']['connect_timeout']
        )
        
        proxy_url = get_proxy_url()
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            url = f"https://api.telegram.org/bot{token}/getMe"
            
            async with session.get(url, proxy=proxy_url) as response:
                if response.status == 200:
                    logger.info("✅ نجح اختبار Telegram API")
                    return True
                else:
                    logger.error(f"❌ فشل اختبار Telegram API: {response.status}")
                    return False
                    
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار Telegram API: {e}")
        return False

def apply_network_optimizations():
    """تطبيق تحسينات الشبكة على مستوى النظام"""
    try:
        import socket
        import platform

        # تحسينات خاصة بـ Windows
        if platform.system() == "Windows":
            # تعطيل Nagle's algorithm لتحسين الاستجابة
            socket.TCP_NODELAY = 1

            # تحسين buffer sizes
            socket.SO_RCVBUF = 65536
            socket.SO_SNDBUF = 65536

            logger.info("✅ تم تطبيق تحسينات Windows")

        # تحسينات عامة
        # تفعيل TCP keepalive
        socket.SO_KEEPALIVE = 1

        logger.info("✅ تم تطبيق تحسينات الشبكة")

    except Exception as e:
        logger.warning(f"⚠️ فشل في تطبيق بعض تحسينات الشبكة: {e}")

def get_connection_retry_config() -> Dict[str, Any]:
    """الحصول على إعدادات إعادة المحاولة"""
    return NETWORK_CONFIG['retry'].copy()

async def retry_with_backoff(func, *args, **kwargs):
    """تنفيذ دالة مع إعادة المحاولة والتأخير التدريجي المحسن"""
    retry_config = get_connection_retry_config()

    max_retries = retry_config['max_retries']
    base_delay = retry_config['retry_delay']
    backoff_factor = retry_config['backoff_factor']
    max_delay = retry_config['max_delay']
    exponential_backoff = retry_config.get('exponential_backoff', True)

    last_exception = None

    for attempt in range(max_retries):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            
            delay = min(base_delay * (backoff_factor ** attempt), max_delay)
            logger.warning(f"⚠️ المحاولة {attempt + 1} فشلت: {e}. إعادة المحاولة خلال {delay} ثانية...")
            await asyncio.sleep(delay)

def log_network_info():
    """عرض معلومات الشبكة"""
    logger.info("🌐 إعدادات الشبكة:")
    logger.info(f"   Connect Timeout: {NETWORK_CONFIG['timeouts']['connect_timeout']}s")
    logger.info(f"   Read Timeout: {NETWORK_CONFIG['timeouts']['read_timeout']}s")
    logger.info(f"   Max Retries: {NETWORK_CONFIG['retry']['max_retries']}")
    logger.info(f"   Connection Pool Size: {NETWORK_CONFIG['connection_pool']['pool_size']}")
    
    proxy_url = get_proxy_url()
    if proxy_url:
        logger.info(f"   Proxy: {proxy_url}")
    else:
        logger.info("   Proxy: غير مفعل")

def handle_windows_socket_error(error):
    """معالجة أخطاء Windows Socket المحددة"""
    import platform

    if platform.system() != "Windows":
        return False

    error_code = getattr(error, 'winerror', None)

    # WinError 10057: A request to send or receive data was disallowed because the socket is not connected
    if error_code == 10057:
        logger.warning("🔧 Windows Socket Error 10057: إعادة تهيئة الاتصال...")
        return True

    # WinError 10054: An existing connection was forcibly closed by the remote host
    elif error_code == 10054:
        logger.warning("🔧 Windows Socket Error 10054: الاتصال مقطوع من الخادم البعيد")
        return True

    # WinError 10060: A connection attempt failed because the connected party did not properly respond
    elif error_code == 10060:
        logger.warning("🔧 Windows Socket Error 10060: انتهت مهلة الاتصال")
        return True

    return False

def create_resilient_socket():
    """إنشاء socket مقاوم للأخطاء"""
    import socket
    import platform

    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

        # تحسينات Windows
        if platform.system() == "Windows":
            # تفعيل TCP keepalive
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)

            # تحسين buffer sizes
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 65536)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 65536)

            # تعطيل Nagle's algorithm
            sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)

        return sock

    except Exception as e:
        logger.error(f"❌ فشل في إنشاء socket محسن: {e}")
        return socket.socket(socket.AF_INET, socket.SOCK_STREAM)

# تطبيق التحسينات عند استيراد الملف
apply_network_optimizations()
