#!/bin/bash
# سكريبت تشغيل البوت - Bot Start Script

echo "🚀 بدء تشغيل البوت..."
echo "🚀 Starting Bot..."

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 غير مثبت"
    echo "❌ Python3 is not installed"
    exit 1
fi

# التحقق من وجود pip
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 غير مثبت"
    echo "❌ pip3 is not installed"
    exit 1
fi

# التحقق من وجود ملف .env
if [ ! -f ".env" ]; then
    echo "⚠️ ملف .env غير موجود"
    echo "⚠️ .env file not found"
    
    if [ -f ".env.example" ]; then
        echo "📝 نسخ .env.example إلى .env"
        echo "📝 Copying .env.example to .env"
        cp .env.example .env
        echo "✅ تم إنشاء ملف .env"
        echo "✅ .env file created"
        echo "📋 يرجى تحديث ملف .env بالمعلومات الصحيحة"
        echo "📋 Please update .env file with correct information"
        exit 1
    else
        echo "❌ ملف .env.example غير موجود"
        echo "❌ .env.example file not found"
        exit 1
    fi
fi

# تثبيت المتطلبات
echo "📦 تثبيت المتطلبات..."
echo "📦 Installing requirements..."

if [ -f "requirements.txt" ]; then
    pip3 install -r requirements.txt
    if [ $? -eq 0 ]; then
        echo "✅ تم تثبيت المتطلبات بنجاح"
        echo "✅ Requirements installed successfully"
    else
        echo "❌ فشل في تثبيت المتطلبات"
        echo "❌ Failed to install requirements"
        exit 1
    fi
else
    echo "❌ ملف requirements.txt غير موجود"
    echo "❌ requirements.txt file not found"
    exit 1
fi

# إنشاء المجلدات المطلوبة
echo "📁 إنشاء المجلدات المطلوبة..."
echo "📁 Creating required directories..."

mkdir -p logs
mkdir -p temp
mkdir -p user_customizations

echo "✅ تم إنشاء المجلدات"
echo "✅ Directories created"

# تشغيل البوت
echo "🤖 تشغيل البوت..."
echo "🤖 Starting bot..."

python3 main.py

echo "🛑 تم إيقاف البوت"
echo "🛑 Bot stopped"
